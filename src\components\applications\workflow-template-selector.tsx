"use client";

/**
 * Enhanced Workflow Template Selector Component
 *
 * This component provides a modern, user-friendly interface for changing workflow templates
 * on existing applications. It features:
 *
 * - Dynamic service ID filtering for relevant templates
 * - Modern UI with contemporary styling and visual hierarchy
 * - User confirmation dialog to prevent accidental changes
 * - Comprehensive error handling and user feedback
 * - Proper loading and empty states
 *
 * @version 2.5.0
 * <AUTHOR> System Enhancement Team
 */

import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Loader2, Workflow, AlertTriangle } from "lucide-react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { apiUrl } from "@/utils/urls";

/**
 * Workflow Template Interface
 * Represents a workflow template with its basic properties
 */
interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  serviceType?: string;
  serviceId?: string;
}

/**
 * Props for WorkflowTemplateSelector Component
 *
 * @param applicationId - The ID of the application being modified
 * @param serviceId - Dynamic service ID from application data for filtering templates
 * @param currentWorkflowTemplateId - ID of the currently assigned workflow template
 * @param currentWorkflowTemplateName - Name of the currently assigned workflow template
 * @param onUpdate - Callback function to refresh parent component after successful update
 */
interface WorkflowTemplateSelectorProps {
  applicationId: string;
  serviceId?: string; // Dynamic service ID from application data
  currentWorkflowTemplateId?: string;
  currentWorkflowTemplateName?: string;
  onUpdate?: () => void;
}

/**
 * Enhanced Workflow Template Selector Component
 *
 * Provides a modern interface for changing workflow templates with:
 * - Dynamic service ID filtering
 * - User confirmation for changes
 * - Modern UI design with proper loading states
 * - Comprehensive error handling
 *
 * @param {WorkflowTemplateSelectorProps} props - The component props
 * @return {JSX.Element} The rendered workflow template selector component
 */
export const WorkflowTemplateSelector: React.FC<
  WorkflowTemplateSelectorProps
> = ({
  applicationId,
  serviceId,
  currentWorkflowTemplateId,
  currentWorkflowTemplateName,
  onUpdate,
}) => {
  const { data: session } = useSession();

  // Component state management
  const [workflowTemplates, setWorkflowTemplates] = useState<
    WorkflowTemplate[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);

  // Confirmation dialog state
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingTemplateId, setPendingTemplateId] = useState<string>("");
  const [pendingTemplateName, setPendingTemplateName] = useState<string>("");

  // Fetch workflow templates filtered by service ID
  useEffect(() => {
    const fetchWorkflowTemplates = async () => {
      if (!session?.backendTokens?.accessToken) return;

      setLoading(true);
      try {
        // Build API URL with dynamic service ID or fallback to immigration default
        const apiServiceId =
          serviceId || "cmcp500ap0000istocjrscl50p0000istocjrscl50"; // Fallback for backward compatibility
        const response = await fetch(
          `${apiUrl}/workflow-templates?serviceId=${apiServiceId}&limit=100`,
          {
            headers: {
              Authorization: `Bearer ${session.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          setWorkflowTemplates(data.data || []);
        } else {
          console.error("Failed to fetch workflow templates");
        }
      } catch (error) {
        console.error("Error fetching workflow templates:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflowTemplates();
  }, [session, serviceId]);

  const handleWorkflowTemplateChange = (newWorkflowTemplateId: string) => {
    if (!session?.backendTokens?.accessToken || updating) return;

    // Find the selected template name for confirmation dialog
    const selectedTemplate = workflowTemplates.find(
      (t) => t.id === newWorkflowTemplateId
    );
    const templateName = selectedTemplate?.name || "Unknown Template";

    // Set pending change and show confirmation dialog
    setPendingTemplateId(newWorkflowTemplateId);
    setPendingTemplateName(templateName);
    setShowConfirmDialog(true);
  };

  /**
   * Confirms and executes the workflow template change using the new API endpoint.
   * This function is called after user confirmation in the dialog.
   *
   * API Integration: POST /api/applications/assign-workflow-template
   * Payload Format: {
   *   "application_id": "cmcw0y00n0004iskg9j6z8bsy",
   *   "new_workflow_template_id": "cmcw0qk9p0006is5gwy6i8icw"
   * }
   */
  const confirmWorkflowTemplateChange = async () => {
    if (!session?.backendTokens?.accessToken || !pendingTemplateId) return;

    setUpdating(true);
    setShowConfirmDialog(false);

    try {
      // Use the new POST /api/applications/assign-workflow-template endpoint
      const response = await fetch(
        `/api/applications/assign-workflow-template`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${session.backendTokens.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            application_id: applicationId,
            new_workflow_template_id: pendingTemplateId,
          }),
        }
      );

      if (response.ok) {
        toast.success("Workflow template updated successfully", {
          description: `Changed to "${pendingTemplateName}"`,
        });
        onUpdate?.();
      } else {
        const errorData = await response.json();
        toast.error("Failed to update workflow template", {
          description: errorData.message || "Please try again later",
        });
      }
    } catch (error) {
      console.error("Error updating workflow template:", error);
      toast.error("Failed to update workflow template", {
        description: "Please try again later",
      });
    } finally {
      setUpdating(false);
      setPendingTemplateId("");
      setPendingTemplateName("");
    }
  };

  const cancelWorkflowTemplateChange = () => {
    setShowConfirmDialog(false);
    setPendingTemplateId("");
    setPendingTemplateName("");
  };

  if (loading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground bg-muted/50 rounded-md border border-dashed">
        <Loader2 className="h-3 w-3 animate-spin" />
        <span>Loading templates...</span>
      </div>
    );
  }

  return (
    <>
      <div className="w-[240px]">
        <div className="space-y-1">
          {/* Current Template Display with Fallback */}
          <div className="flex items-center gap-2 mb-2">
            <Workflow className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">Current:</span>
            {currentWorkflowTemplateName ? (
              <Badge variant="secondary" className="text-xs font-medium">
                {currentWorkflowTemplateName}
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="text-xs text-muted-foreground"
              >
                No template assigned
              </Badge>
            )}
          </div>

          {/* Template Selector */}
          <Select
            value={currentWorkflowTemplateId || ""}
            onValueChange={handleWorkflowTemplateChange}
            disabled={updating}
          >
            <SelectTrigger className="h-9 text-sm border-2 hover:border-primary/50 transition-colors focus:ring-2 focus:ring-primary/20">
              <div className="flex items-center gap-2">
                {updating && <Loader2 className="h-3 w-3 animate-spin" />}
                <SelectValue
                  placeholder="Change workflow template..."
                  className="text-sm"
                />
              </div>
            </SelectTrigger>
            <SelectContent className="max-w-[300px]">
              {workflowTemplates.length === 0 ? (
                <div className="flex items-center gap-2 p-4 text-sm text-muted-foreground">
                  <AlertTriangle className="h-4 w-4" />
                  <span>No workflow templates available for this service</span>
                </div>
              ) : (
                workflowTemplates.map((template) => (
                  <SelectItem
                    key={template.id}
                    value={template.id}
                    className="cursor-pointer"
                  >
                    <div className="flex flex-col gap-1 py-1">
                      <div className="font-medium text-sm">{template.name}</div>
                      {template.description && (
                        <div className="text-xs text-muted-foreground line-clamp-2 max-w-[250px]">
                          {template.description}
                        </div>
                      )}
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Modern Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/20">
                <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
              </div>
              <AlertDialogTitle className="text-lg font-semibold">
                Confirm Workflow Template Change
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-sm leading-relaxed">
              <div className="space-y-3">
                <p>
                  You&apos;re about to change the workflow template to{" "}
                  <span className="font-semibold text-foreground">
                    &quot;{pendingTemplateName}&quot;
                  </span>
                  .
                </p>

                <div className="rounded-lg bg-muted/50 p-3 border border-amber-200 dark:border-amber-800">
                  <p className="font-medium text-amber-800 dark:text-amber-200 mb-2">
                    ⚠️ Important Considerations:
                  </p>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• Updates the application&apos;s workflow process</li>
                    <li>• May affect current step progression</li>
                    <li>• Could impact document requirements and forms</li>
                    <li>• This change cannot be easily undone</li>
                  </ul>
                </div>

                <p className="text-xs text-muted-foreground">
                  Please ensure this change is intentional before proceeding.
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="gap-2">
            <AlertDialogCancel
              onClick={cancelWorkflowTemplateChange}
              className="hover:bg-muted"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmWorkflowTemplateChange}
              className="bg-amber-600 text-white hover:bg-amber-700 focus:ring-amber-500"
            >
              Confirm Change
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
